# Module Implementation Plan

This document outlines the implementation strategy for DICOM modules with a focus on user-friendly IntelliSense support and intuitive APIs. The PatientModule serves as the exemplar for all future module implementations.

## Core User Experience Features

**PatientModule Demonstrates Clean IntelliSense Through Method-Based API:**

### 🎯 Clean IntelliSense Design
- **Method-Only Interface**: No direct DICOM attribute access to avoid IntelliSense clutter
- **Logical Method Grouping**: Clear separation of construction, configuration, and validation methods
- **Type-Safe Parameters**: Full IDE autocomplete with parameter types and enum support
- **Method Chaining**: Fluent API enables `from_required_elements().with_optional_elements()`

### 🏗️ DICOM Type-Based Parameter Design
- **Type 1 as Required Args**: `from_required_elements(type1_param)` - user must provide value
- **Type 2 as Kwargs with Defaults**: `from_required_elements(type2_param="")` - required but can be empty
- **Type 3 as Optional**: `with_optional_elements(type3_param=None)` - truly optional elements
- **Conditional Types**: `with_[...]()` handles Type 1C/2C based on context

### 🧩 Method Categories by DICOM Type
- **`from_required_elements()`**: All Type 1 (args) + Type 2 (kwargs with empty defaults)
- **`with_optional_elements()`**: All Type 3 elements (kwargs with None defaults)  
- **`with_[...]()`**: Type 1C/2C elements with validation logic
- **`create_*_item()`**: Static helpers for sequence item creation

### 🔍 Logical Properties & Validation
- **State Checks**: `is_human`, `is_non_human`, `is_deidentified`, `has_alternative_calendar_dates`
- **Validation**: `validate()` returns structured error/warning dictionary
- **Save Integration**: `save_as(validate=True)` ensures data integrity

## Module Implementation Template

**All future modules must follow the PatientModule DICOM Type-based design:**

```python
class NewModule(Dataset):
    """Module description - DICOM PS3.3 reference.
    
    Usage:
        module = NewModule.from_required_elements(
            type1_required_param,                    # Type 1: User must provide
            type2_param="",                         # Type 2: Required but can be empty
            type2_another=""
        ).with_optional_elements(
            type3_param=None,                       # Type 3: Optional
            type3_another=None
        ).with_conditional_group(
            type1c_param,                           # Type 1C: Required if condition met
            type2c_param=""                         # Type 2C: Required but empty if condition met
        )
    """
    
    @classmethod
    def from_required_elements(
        cls, 
        # Type 1 elements as positional args (user MUST provide value)
        required_param: str,
        # Type 2 elements as kwargs with empty string defaults (required but can be empty)
        optional_but_present_param: str = "",
        another_type2_param: str = ""
    ) -> 'NewModule':
        """Create module with all required (Type 1 and Type 2) elements.
        
        Args:
            required_param: Description (tag) Type 1
            optional_but_present_param: Description (tag) Type 2
            another_type2_param: Description (tag) Type 2
        """
        instance = cls()
        # Set DICOM attributes (internal implementation)
        instance.RequiredParam = required_param
        instance.OptionalButPresentParam = optional_but_present_param
        instance.AnotherType2Param = another_type2_param
        return instance
    
    def with_optional_elements(
        self,
        # Type 3 elements as kwargs with None defaults (truly optional)
        optional_param: str | None = None,
        another_optional: str | None = None
    ) -> 'NewModule':
        """Add optional (Type 3) elements."""
        if optional_param is not None:
            self.OptionalParam = optional_param
        if another_optional is not None:
            self.AnotherOptional = another_optional
        return self
    
    def with_conditional_group(
        self,
        # Type 1C/2C elements with validation
        conditional_param: str,
        conditional_optional: str = ""
    ) -> 'NewModule':
        """Add conditional elements with required validation logic."""
        # Validate conditional requirements before setting
        if some_condition_not_met:
            raise ValueError("conditional_param required when condition X is true")
        
        self.ConditionalParam = conditional_param
        self.ConditionalOptional = conditional_optional
        return self
    
    @staticmethod
    def create_sequence_item(
        required_item_param: str,
        optional_item_param: str | None = None
    ) -> dict[str, any]:
        """Create sequence items for complex structures."""
        item = {'RequiredItemParam': required_item_param}
        if optional_item_param is not None:
            item['OptionalItemParam'] = optional_item_param
        return item
    
    @property
    def is_configured(self) -> bool:
        """Logical state check based on internal data."""
        return hasattr(self, 'RequiredParam')
    
    @property  
    def has_optional_data(self) -> bool:
        """Check if optional elements are present."""
        return hasattr(self, 'OptionalParam')
    
    def validate(self, config: ValidationConfig = None) -> dict[str, list[str]]:
        """Validate module data against DICOM standard."""
        return ModuleValidator.validate(self, config)
```

## Key Design Principles

### 🎯 Module Success Criteria
1. **Clean IntelliSense**: Only methods and properties visible, no raw DICOM attributes
2. **DICOM Type Clarity**: Type 1 (args), Type 2 (kwargs=""), Type 3 (kwargs=None), Conditional (validated)
3. **Method-Based API**: All data setting through explicit named parameter methods
4. **Logical Properties**: `is_*` and `has_*` properties for common state checks
5. **Validation Integration**: Built-in validation with structured error/warning reporting

### 🏗️ Implementation Requirements
- **Inherit from Dataset**: Direct pydicom.Dataset inheritance for native DICOM handling
- **Named Parameters**: Every DICOM element visible as explicit method parameter
- **Type Safety**: Full type hints for IDE support and runtime validation
- **Error Handling**: Clear validation errors with DICOM tag references
- **Documentation**: Google-style docstrings with DICOM references

### ✅ Validation Standards
- **On-Demand**: `validate()` method returns `{"errors": [], "warnings": []}`
- **Configurable**: `ValidationConfig` parameter for customization
- **Structured**: No exceptions during validation

## Module Categories

**All modules follow the PatientModule pattern with method-based APIs:**

### 🏥 Patient Information Modules
- [ ] **PatientModule** (C.7.1.1) - ✅ Complete exemplar implementation
- [ ] **ClinicalTrialSubjectModule** (C.7.1.3) - Clinical trial patient data
- [ ] **PatientStudyModule** (C.7.2.2) - Patient study information

### 📋 Study & Series Modules  
- [ ] **GeneralStudyModule** (C.7.2.1) - Study-level information
- [ ] **ClinicalTrialStudyModule** (C.7.2.3) - Clinical trial study data
- [ ] **GeneralSeriesModule** (C.7.3.1) - General series information
- [ ] **RTSeriesModule** (C.8.8.1) - RT-specific series information
- [ ] **ClinicalTrialSeriesModule** (C.7.3.2) - Clinical trial series data

### 🖼️ Image & Spatial Modules
- [ ] **GeneralImageModule** (C.7.6.1) - General image information
- [ ] **ImagePlaneModule** (C.7.6.2) - Image geometry and orientation
- [ ] **ImagePixelModule** (C.7.6.3) - Pixel data characteristics
- [ ] **MultiFrameModule** (C.7.6.6) - Multi-frame image data
- [ ] **FrameOfReferenceModule** (C.7.4.1) - Spatial reference frame

### ⚡ Radiotherapy Modules
- [ ] **RTDoseModule** (C.8.8.3) - Dose distribution data
- [ ] **RTDVHModule** (C.8.8.4) - Dose-volume histogram
- [ ] **RTImageModule** (C.8.8.2) - RT image information
- [ ] **RTGeneralPlanModule** (C.8.8.9) - Basic RT plan information
- [ ] **StructureSetModule** (C.8.8.5) - Structure set information

### 🔧 Equipment & Common Modules
- [ ] **GeneralEquipmentModule** (C.7.5.1) - Equipment/device information
- [ ] **SOPCommonModule** (C.12.1) - SOP instance identification
- [ ] **CommonInstanceReferenceModule** (C.12.2) - Instance references

## Module Testing Standards

**Each module must pass comprehensive testing before implementation completion:**

### 🧪 Unit Tests (Required for Each Module)
- [ ] **Factory Method Tests**: `from_required_elements()` with valid/invalid Type 1/2 parameters
- [ ] **Builder Method Tests**: All `with_*()` methods with valid/invalid optional parameters
- [ ] **Static Helper Tests**: `create_*_item()` methods for sequence item construction
- [ ] **Property Tests**: All `is_*` and `has_*` properties return expected boolean values
- [ ] **Validation Tests**: `validate()` method returns proper error/warning structure

### 🎯 IntelliSense Verification (Manual Testing)
- [ ] **Method Visibility**: Only factory, builder, helper, and property methods visible
- [ ] **Parameter Clarity**: All DICOM elements clearly named as method parameters
- [ ] **Type Hints**: Full IDE autocomplete with parameter types and return values
- [ ] **Documentation**: Method docstrings show DICOM tags and element types

### ✅ DICOM Compliance Testing  
- [ ] **Dataset Generation**: Module creates valid pydicom.Dataset with proper DICOM attributes
- [ ] **Tag Validation**: All DICOM tags match PS3.6 data dictionary
- [ ] **VR Compliance**: Value representations match DICOM standard requirements
- [ ] **Type Requirements**: Type 1/2/3/1C/2C elements handled according to DICOM rules

## Implementation Dependencies

**Build modules in logical dependency order:**

### 🥇 Priority 1 - Foundation Modules
1. **PatientModule** - ✅ Complete (exemplar implementation)
2. **GeneralStudyModule** - Study-level identification 
3. **GeneralEquipmentModule** - Equipment/manufacturer information
4. **SOPCommonModule** - SOP instance identification

### 🥈 Priority 2 - Series & Image Modules  
5. **GeneralSeriesModule** - Series-level information
6. **RTSeriesModule** - RT-specific series information
7. **FrameOfReferenceModule** - Spatial coordinate system
8. **GeneralImageModule** - Basic image information

### 🥉 Priority 3 - Specialized Modules
9. **RTDoseModule** - Dose distribution data
10. **ImagePixelModule** - Pixel data characteristics
11. **ImagePlaneModule** - Image geometry and positioning
12. Additional modules as needed...

**Note**: Each module is self-contained and can be implemented independently following the PatientModule pattern.