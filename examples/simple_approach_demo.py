"""Simple Approach Demo - Clean IOD Implementation

This demonstrates the much simpler composition-based approach vs. multiple inheritance.
Compare this with the existing complex approach to see the benefits.
"""

import numpy as np
from datetime import datetime
from pydicom.valuerep import PersonName

# Import the simplified implementations
from pyrt_dicom.modules.patient_module import PatientModule
from pyrt_dicom.enums.patient_enums import PatientSex
from pyrt_dicom.enums.dose_enums import DoseUnits, DoseType
from pyrt_dicom.iods.simple_rt_dose_iod import RTDose


def main():
    """Demonstrate the simplified approach."""
    print("=== PyRT-DICOM Simplified Approach Demo ===\\n")
    
    # Step 1: Create Patient Module (same as before - this works well)
    print("1. Creating Patient Module...")
    patient = PatientModule.from_required_elements(
        patients_name=PersonName.from_named_components("<PERSON>", "<PERSON>"),
        patient_id="DEMO001",
        patients_birth_date=datetime(1986, 12, 1, 10, 15, 0),
        patients_sex=PatientSex.MALE
    ).with_optional_elements(
        patient_comments="Demo patient for simplified approach"
    )
    
    # Validate patient
    patient_result = patient.validate()
    print(f"   Patient validation: {len(patient_result['errors'])} errors, {len(patient_result['warnings'])} warnings")
    
    # Step 2: Create RT Dose using simplified composition approach
    print("\\n2. Creating RT Dose IOD (simplified approach)...")
    rt_dose = RTDose.from_patient_module(
        patient_module=patient,
        dose_units=DoseUnits.GY,
        dose_type=DoseType.PHYSICAL
    )
    
    print(f"   RT Dose created: {rt_dose}")
    
    # Step 3: Add dose grid data
    print("\\n3. Adding dose grid data...")
    
    # Create sample 3D dose data
    dose_array = np.random.random((50, 50, 20)) * 100  # 50x50x20 dose grid
    pixel_data = dose_array.astype(np.float32).tobytes()
    
    rt_dose.with_dose_grid_data(
        pixel_data=pixel_data,
        rows=50,
        columns=50,
        pixel_spacing=[2.0, 2.0],
        image_position_patient=[-50.0, -50.0, 0.0],
        number_of_frames=20
    ).with_dose_scaling(
        dose_grid_scaling=0.001
    )
    
    print(f"   Dose grid added: {rt_dose.Rows}x{rt_dose.Columns}x{getattr(rt_dose, 'NumberOfFrames', 1)}")
    print(f"   Is 3D dose: {rt_dose.is_3d_dose}")
    print(f"   Has dose grid: {rt_dose.has_dose_grid}")
    
    # Step 4: Demonstrate property delegation (IntelliSense benefits)
    print("\\n4. Accessing patient data through RT Dose (property delegation)...")
    print(f"   Patient name: {rt_dose.patients_name}")
    print(f"   Patient ID: {rt_dose.patient_id}")
    print(f"   Patient sex: {rt_dose.patients_sex}")
    
    # Step 5: Access dose-specific properties
    print("\\n5. Accessing dose-specific data...")
    print(f"   Dose units: {rt_dose.DoseUnits} (enum: {rt_dose.dose_units_enum})")
    print(f"   Dose type: {rt_dose.DoseType} (enum: {rt_dose.dose_type_enum})")
    print(f"   Modality: {rt_dose.Modality}")
    print(f"   SOP Class UID: {rt_dose.SOPClassUID}")
    
    # Step 6: Validate the complete RT Dose
    print("\\n6. Validating complete RT Dose...")
    dose_result = rt_dose.validate()
    print(f"   RT Dose validation: {len(dose_result['errors'])} errors, {len(dose_result['warnings'])} warnings")
    
    if dose_result['errors']:
        print("   Errors:")
        for error in dose_result['errors']:
            print(f"     - {error}")
    
    if dose_result['warnings']:
        print("   Warnings:")
        for warning in dose_result['warnings']:
            print(f"     - {warning}")
    
    # Step 7: Show module composition
    print("\\n7. Module composition (much cleaner than multiple inheritance)...")
    print(f"   Modules in RT Dose: {list(rt_dose.modules.keys())}")
    print(f"   Patient module type: {type(rt_dose.patient_module)}")
    
    # Step 8: Save the DICOM file
    print("\\n8. Saving DICOM file...")
    try:
        if not dose_result['errors']:
            rt_dose.save_as("simple_rtdose_demo.dcm")
            print("   ✅ RT Dose saved successfully as 'simple_rtdose_demo.dcm'")
        else:
            print("   ❌ Cannot save - validation errors present")
    except Exception as e:
        print(f"   ❌ Save failed: {e}")
    
    print("\\n=== Benefits of Simplified Approach ===")
    print("✅ Much simpler inheritance (only SimpleBaseIOD -> FileDataset)")
    print("✅ Clean module composition instead of complex multiple inheritance")
    print("✅ Maintains all IntelliSense benefits through property delegation")
    print("✅ Easier to understand and maintain")
    print("✅ Proper FileDataset inheritance for DICOM file handling")
    print("✅ Follows 'composition over inheritance' best practice")
    print("✅ Stays true to POC principles - simple and testable")
    
    print("\\n=== Comparison with Multiple Inheritance Approach ===")
    print("❌ Multiple inheritance: RTDose(BaseIOD, PatientModule, RTDoseModule, SOPCommonModule)")
    print("✅ Simple composition: SimpleRTDose(SimpleBaseIOD) + patient_module attribute")
    print("❌ Complex MRO and potential diamond problem")  
    print("✅ Clear, linear inheritance hierarchy")
    print("❌ Tight coupling between IOD and modules")
    print("✅ Loose coupling - modules can be swapped/modified independently")


if __name__ == "__main__":
    main()