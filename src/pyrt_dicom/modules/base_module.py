"""
Base Module - Abstract base class for all DICOM modules.

This abstract base class provides the common interface and functionality
that all DICOM modules should implement, while inheriting from pydicom.Dataset
for native DICOM data handling.

IMPORTANT: Modules are NOT intended to be saved! Do NOT include any file 
metadata or save methods in module classes, as these are reserved for IOD classes.
"""
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional
from pydicom import Dataset
from ..validators.base_validator import ValidationConfig


class BaseModule(Dataset, ABC):
    """Abstract base class for all DICOM modules.
    
    Inherits from pydicom.Dataset to provide native DICOM data handling.
    All DICOM modules should inherit from this class and implement the
    required abstract methods to ensure consistent API patterns.
    
    Key Design Principles:
    - Clean IntelliSense: Only methods and properties visible, no raw DICOM attributes
    - DICOM Type Clarity: Type 1 (args), Type 2 (kwargs=""), Type 3 (kwargs=None)
    - Method-Based API: All data setting through explicit named parameter methods
    - Fluent Interface: Methods return self for chaining
    - Validation Integration: Built-in validation with structured error/warning reporting
    
    Note: Modules are NOT intended to be saved directly. File operations and
    metadata are handled by IOD classes that compose modules.
    """
    
    @classmethod
    @abstractmethod
    def from_required_elements(cls, *args, **kwargs) -> 'BaseModule':
        """Create module instance from all required (Type 1 and Type 2) data elements.
        
        This method must be implemented by each module subclass to define their
        specific required elements according to the DICOM standard.
        
        Pattern:
        - Type 1 elements as positional args (user MUST provide value)
        - Type 2 elements as kwargs with empty string defaults (required but can be empty)
        
        Returns:
            BaseModule: New module instance with required data elements set
        """
        pass
    
    def validate(self, config: Optional[ValidationConfig] = None) -> Dict[str, List[str]]:
        """Validate this module instance against DICOM standard.
        
        This method should be overridden by subclasses to provide module-specific
        validation logic using their corresponding validator classes.
        
        Args:
            config (ValidationConfig | None): Optional validation configuration
            
        Returns:
            Dict with 'errors' and 'warnings' lists
        """
        # Default implementation returns no errors/warnings
        # Subclasses should override this with their specific validator
        return {"errors": [], "warnings": []}
    
    def _set_attribute_if_not_none(self, attr_name: str, value: Any) -> None:
        """Helper method to set DICOM attribute only if value is not None.
        
        This is a common pattern in with_* methods for optional elements.
        
        Args:
            attr_name (str): DICOM attribute name to set
            value (Any): Value to set, ignored if None
        """
        if value is not None:
            setattr(self, attr_name, value)
    
    def _validate_conditional_requirement(
        self, 
        condition: bool, 
        required_values: List[Any], 
        error_message: str
    ) -> None:
        """Helper method to validate conditional (Type 1C/2C) requirements.
        
        Args:
            condition (bool): Whether the conditional requirement applies
            required_values (List[Any]): Values that must be provided when condition is true
            error_message (str): Error message to raise if validation fails
            
        Raises:
            ValueError: If conditional requirement is not met
        """
        if condition and all(value is None for value in required_values):
            raise ValueError(error_message)
    
    def _format_date_value(self, date_value: Any) -> str:
        """Helper method to format date values to DICOM DA format (YYYYMMDD).
        
        Args:
            date_value: Date value (str, datetime, or date object)
            
        Returns:
            str: Date formatted as YYYYMMDD string
        """
        from datetime import datetime, date
        
        if isinstance(date_value, (datetime, date)):
            return date_value.strftime("%Y%m%d")
        return str(date_value)
    
    def _format_time_value(self, time_value: Any) -> str:
        """Helper method to format time values to DICOM TM format (HHMMSS).
        
        Args:
            time_value: Time value (str or datetime object)
            
        Returns:
            str: Time formatted as HHMMSS string
        """
        from datetime import datetime
        
        if isinstance(time_value, datetime):
            if time_value.microsecond:
                return f"{time_value.strftime('%H%M%S')}.{time_value.microsecond:06d}"
            return time_value.strftime("%H%M%S")
        return str(time_value)
    
    def _format_enum_value(self, enum_value: Any) -> str:
        """Helper method to extract value from enum objects.
        
        Args:
            enum_value: Enum object or string value
            
        Returns:
            str: String value from enum or original string
        """
        return enum_value.value if hasattr(enum_value, 'value') else str(enum_value)
    
    @property
    def module_name(self) -> str:
        """Get the name of this module class.
        
        Returns:
            str: Module class name
        """
        return self.__class__.__name__
    
    @property
    def has_data(self) -> bool:
        """Check if this module contains any DICOM data elements.
        
        Returns:
            bool: True if module has any data elements
        """
        return len(self) > 0
    
    def get_element_count(self) -> int:
        """Get the number of DICOM data elements in this module.
        
        Returns:
            int: Number of data elements
        """
        return len(self)
    
    def get_element_tags(self) -> List[str]:
        """Get list of DICOM tags present in this module.
        
        Returns:
            List[str]: List of DICOM tag strings
        """
        return [str(elem.tag) for elem in self]
    
    def __repr__(self) -> str:
        """String representation of the module.
        
        Returns:
            str: Module representation with class name and element count
        """
        return f"{self.module_name}(elements={self.get_element_count()})"
