"""DICOM Modules - Concrete implementations of DICOM specification modules.

Each module represents a specific section of the DICOM standard, implemented as
concrete Python classes with all data elements as attributes for IntelliSense support.

All modules inherit from BaseModule for consistent API patterns and validation.
"""

# Concrete module implementations
from .patient_module import PatientModule
from .clinical_trial_subject_module import ClinicalTrialSubjectModule
from .patient_study_module import PatientStudyModule

__all__ = [
    "PatientModule",
    "ClinicalTrialSubjectModule",
    "PatientStudyModule",
]
