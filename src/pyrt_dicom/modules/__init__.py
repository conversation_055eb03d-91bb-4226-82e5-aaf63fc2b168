"""DICOM Modules - Concrete implementations of DICOM specification modules.

Each module represents a specific section of the DICOM standard, implemented as
concrete Python classes with all data elements as attributes for IntelliSense support.

All modules inherit from BaseModule for consistent API patterns and validation.
"""

# Base module class
from .base_module import BaseModule

# Concrete module implementations
from .patient_module import PatientModule

__all__ = [
    "BaseModule",
    "PatientModule",
]