"""Clinical Trial-related DICOM enumerations."""

from enum import Enum


class ConsentForDistribution(Enum):
    """Consent for Distribution (0012,0083) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.7.2.3:
    - NO = No consent for distribution
    - YES = Consent for distribution granted
    - WITHDRAWN = Consent withdrawn
    """
    NO = "NO"
    YES = "YES"
    WITHDRAWN = "WITHDRAWN"


class DistributionType(Enum):
    """Distribution Type (0012,0084) - DICOM VR: CS
    
    Defined Terms per DICOM PS3.3 C.7.2.3:
    - NAMED_PROTOCOL = Conducting the named protocol
    - RESTRICTED_REUSE = Re-use for restricted purposes
    - PUBLIC_RELEASE = Public release
    """
    NAMED_PROTOCOL = "NAMED_PROTOCOL"
    RESTRICTED_REUSE = "RESTRICTED_REUSE"
    PUBLIC_RELEASE = "PUBLIC_RELEASE"
