"""Base IOD - Clean foundation for DICOM IODs.

Base class that inherits from FileDataset for proper DICOM file handling
without the complexity of multiple inheritance patterns.
"""

from abc import ABC, abstractmethod
from pathlib import Path
from pydicom import FileDataset
from pydicom.uid import generate_uid, ImplicitVR<PERSON>ittleEndian
from pydicom.filereader import FileMetaDataset
from typing import Dict, List, Any, Optional


class BaseIOD(FileDataset, ABC):
    """Base class for DICOM Information Object Definitions.
    
    Inherits from pydicom.FileDataset for proper DICOM file handling.
    Uses composition (not inheritance) for modules to maintain simplicity.
    
    This approach:
    - Is much simpler than multiple inheritance
    - Maintains IntelliSense benefits through proper delegation
    - Follows composition over inheritance principle
    - Stays true to POC constraints (simple, testable, no complex abstractions)
    
    Example:
        >>> class RTDose(BaseIOD):
        ...     SOP_CLASS_UID = "1.2.840.10008.*******.1.481.2"
        ...     
        ...     def __init__(self):
        ...         super().__init__()
        ...         self.patient_module = None
        ...         self.rt_dose_module = None
    """
    
    # Subclasses must define their SOP Class UID
    SOP_CLASS_UID: str = ""
    
    def __init__(self, filename: str = "", dataset=None, preamble: bytes = b"\\x00" * 128):
        """Initialize IOD with proper FileDataset setup.
        
        Args:
            filename: Name for the DICOM file (used by FileDataset)
            dataset: Optional initial dataset (used by FileDataset)  
            preamble: DICOM file preamble (default: standard 128 zero bytes)
        """
        # Initialize FileDataset with proper file meta information
        file_meta = self._create_file_meta()
        super().__init__(filename, dataset, file_meta=file_meta, preamble=preamble)
        
        # Set common IOD attributes
        self.SOPClassUID = self.SOP_CLASS_UID
        self.SOPInstanceUID = generate_uid()
        
        # Initialize module containers (subclasses will populate these)
        self._modules: Dict[str, Any] = {}
    
    def _create_file_meta(self) -> FileMetaDataset:
        """Create proper DICOM file meta information."""
        file_meta = FileMetaDataset()
        file_meta.MediaStorageSOPClassUID = self.SOP_CLASS_UID
        file_meta.MediaStorageSOPInstanceUID = generate_uid()
        file_meta.TransferSyntaxUID = ImplicitVRLittleEndian
        file_meta.ImplementationClassUID = "1.2.826.0.1.3680043.8.498.1"  # PyRT-DICOM
        file_meta.ImplementationVersionName = "PyRT-DICOM 0.1.0"
        return file_meta
    
    @classmethod
    @abstractmethod
    def from_required_modules(cls, **modules) -> 'BaseIOD':
        """Create IOD from required modules.
        
        Abstract method that must be implemented by each IOD subclass to define
        their specific module requirements.
        
        Args:
            **modules: Module instances required by the specific IOD
            
        Returns:
            BaseIOD: IOD instance with module data
        """
        pass

    @abstractmethod
    def with_optional_modules(self, **modules) -> 'BaseIOD':
        """Add optional modules to this IOD.
        
        Abstract method that must be implemented by each IOD subclass to define
        their specific optional module requirements.
        
        Args:
            **modules: Module instances optional by the specific IOD
            
        Returns:
            BaseIOD: IOD instance with module data
        """
        pass
    
    def add_module(self, name: str, module: Any) -> None:
        """Add a DICOM module to this IOD.
        
        Args:
            name: Name identifier for the module
            module: Module instance to add
        """
        self._modules[name] = module
        
        # Copy module data to the main dataset for direct DICOM access
        if hasattr(module, '__iter__'):  # If it's a Dataset-like object
            for elem in module:
                setattr(self, elem.keyword, elem.value)
        else:
            # For other module types, copy non-private attributes
            for attr_name in dir(module):
                if not attr_name.startswith('_') and not callable(getattr(module, attr_name)):
                    try:
                        attr_value = getattr(module, attr_name)
                        # Convert to DICOM keyword format if needed
                        if hasattr(self, attr_name):
                            setattr(self, attr_name, attr_value)
                    except (AttributeError, ValueError):
                        continue
    
    def get_module(self, name: str) -> Optional[Any]:
        """Get a module by name.
        
        Args:
            name: Name identifier for the module
            
        Returns:
            Module instance or None if not found
        """
        return self._modules.get(name)
    
    def validate(self) -> Dict[str, List[str]]:
        """Validate entire IOD including all modules.
        
        Returns:
            Dict with 'errors' and 'warnings' lists
        """
        result = {"errors": [], "warnings": []}
        
        # Validate each module
        for name, module in self._modules.items():
            if hasattr(module, 'validate'):
                try:
                    module_result = module.validate()
                    if isinstance(module_result, dict):
                        result['errors'].extend(module_result.get('errors', []))
                        result['warnings'].extend(module_result.get('warnings', []))
                except Exception as e:
                    result['errors'].append(f"Validation error in {name}: {e}")
        
        # IOD-specific validation
        self._validate_iod_requirements(result)
        
        return result
    
    def _validate_iod_requirements(self, result: Dict[str, List[str]]) -> None:
        """Override in subclasses for IOD-specific validation.
        
        Args:
            result: Validation result to update
        """
        # Base IOD validation
        if not hasattr(self, 'SOPClassUID') or self.SOPClassUID != self.SOP_CLASS_UID:
            result['errors'].append(f"SOP Class UID must be {self.SOP_CLASS_UID}")
        
        if not hasattr(self, 'SOPInstanceUID') or not self.SOPInstanceUID:
            result['errors'].append("SOP Instance UID is required")
    
    def save_as(self, filepath: Path | str, validate: bool = True, **kwargs) -> None:
        """Save IOD as DICOM file.
        
        Args:
            filepath: Path where to save the DICOM file
            validate: Whether to validate before saving (default True)
            **kwargs: Additional arguments passed to FileDataset.save_as()
            
        Raises:
            ValueError: If validation fails and validate=True
        """
        if validate:
            validation_result = self.validate()
            if validation_result.get('errors'):
                error_msg = "; ".join(validation_result['errors'])
                raise ValueError(f"IOD validation failed: {error_msg}")
        
        # Update file meta information before saving
        if hasattr(self, 'file_meta'):
            self.file_meta.MediaStorageSOPInstanceUID = self.SOPInstanceUID
        
        super().save_as(str(filepath), **kwargs)
    
    def __repr__(self) -> str:
        """String representation of the IOD."""
        class_name = self.__class__.__name__
        modules = list(self._modules.keys())
        return f"{class_name}(modules={modules}, sop_instance_uid='{self.SOPInstanceUID}')"