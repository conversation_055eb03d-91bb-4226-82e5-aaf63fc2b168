Metadata-Version: 2.4
Name: pyrt-dicom
Version: 0.1.0
Summary: Python library for creating radiotherapy DICOM files
Project-URL: Documentation, https://github.com/unknown/pyrt-dicom#readme
Project-URL: Issues, https://github.com/unknown/pyrt-dicom/issues
Project-URL: Source, https://github.com/unknown/pyrt-dicom
Author-email: <PERSON> <<EMAIL>>
License-Expression: MIT
Keywords: dicom,imaging,medical,oncology,radiotherapy,rt
Classifier: Development Status :: 4 - Beta
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Requires-Python: >=3.8
Requires-Dist: pydicom>=2.4.0
Provides-Extra: test
Requires-Dist: pytest-cov>=4.0.0; extra == 'test'
Requires-Dist: pytest>=7.0.0; extra == 'test'
Description-Content-Type: text/markdown

# PyRT-DICOM

[![PyPI - Version](https://img.shields.io/pypi/v/pyrt-dicom.svg)](https://pypi.org/project/pyrt-dicom)
[![PyPI - Python Version](https://img.shields.io/pypi/pyversions/pyrt-dicom.svg)](https://pypi.org/project/pyrt-dicom)

-----

**Table of Contents**

- [Installation](#installation)
- [License](#license)

## Installation

```console
pip install pyrt-dicom
```

## PyRT-DICOM ("Pirate DICOM")

Python library for creating radiotherapy DICOM files with CT, RTImage, RTDose, RTPlan, and RTStruct modalities. Built on top of pydicom, this library provides a strongly-typed, IntelliSense-friendly interface for creating DICOM Information Object Definitions (IODs) through modular composition.

### Core Features

- **Modular Architecture**: IODs are composed from individual DICOM modules
- **Type Safety**: All DICOM data elements as class attributes for IntelliSense
- **No Free Text Configuration**: Strongly-typed interfaces with strict enum enforcement
- **Optional Validation**: No validation at creation, validate on-demand or during export
- **Auto-Generated UIDs**: UIDs generated at object creation with cross-dataset linking
- **Memory Storage**: Store all data in memory for fast access and simplicity

### Quick Start (POC)

```python
from pyrt_dicom.modules import (
    PatientModule, GeneralStudyModule, GeneralEquipmentModule,
    RTSeriesModule, FrameOfReferenceModule, RTDoseModule, SOPCommonModule
)
from pyrt_dicom.iods import RTDose
from pyrt_dicom.enums import DoseUnits, DoseType, PatientSex

# Step 1: Create individual DICOM modules
patient = PatientModule(
    patient_name="Doe^John^^^",
    patient_id="12345",
    patient_sex=PatientSex.MALE
)

study = GeneralStudyModule(
    study_description="RT Treatment Planning"
)

equipment = GeneralEquipmentModule(
    manufacturer="Varian Medical Systems"
)

series = RTSeriesModule(
    modality="RTDOSE",
    series_description="Primary Dose Calculation"
)

frame_of_reference = FrameOfReferenceModule()

rt_dose_module = RTDoseModule(
    dose_units=DoseUnits.GY,
    dose_type=DoseType.PHYSICAL,
    dose_comment="Treatment plan dose distribution"
)

sop_common = SOPCommonModule(sop_class_uid=RTDose.SOP_CLASS_UID)

# Step 2: Create RT Dose IOD from modules
rt_dose = RTDose.from_required_modules(
    patient=patient,
    study=study,
    equipment=equipment,
    series=series,
    frame_of_reference=frame_of_reference,
    rt_dose=rt_dose_module,
    sop_common=sop_common
)

# Step 3: Access DICOM data elements with IntelliSense
print(rt_dose.patient_name)      # "Doe^John^^^"
print(rt_dose.dose_units.value)  # "GY"
print(rt_dose.manufacturer)      # "Varian Medical Systems"

# Step 4: Validate and save DICOM file
validation = rt_dose.validate()
if not validation.get('errors'):
    rt_dose.save_dataset("dose.dcm")
```

### Development Status

🚨 **PROOF OF CONCEPT PHASE** 🚨

This project is currently in POC phase, focusing on RT Dose IOD to validate the modular approach using multiple inheritance. No base classes or abstractions until POC success criteria are met.

## License

`pyrt-dicom` is distributed under the terms of the [MIT](https://spdx.org/licenses/MIT.html) license.